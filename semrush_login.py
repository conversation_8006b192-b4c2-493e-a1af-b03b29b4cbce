from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
import time

# -------- CONFIG --------
SEMRUSH_EMAIL = "<EMAIL>"
SEMRUSH_PASSWORD = "your_password"
LOGIN_URL = "https://www.semrush.com/login/"

chrome_options = Options()
chrome_options.add_argument("--start-maximized")

# Automatically downloads the right ChromeDriver
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

try:
    driver.get(LOGIN_URL)
    time.sleep(3)

    driver.find_element(By.NAME, "email").send_keys(SEMRUSH_EMAIL)
    driver.find_element(By.NAME, "password").send_keys(SEMRUSH_PASSWORD)
    driver.find_element(By.NAME, "password").send_keys(Keys.RETURN)

    time.sleep(5)
    print("✅ Logged in successfully!")

except Exception as e:
    print(f"❌ Error: {e}")

finally:
    driver.quit()
